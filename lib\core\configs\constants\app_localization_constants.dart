class AppLocalizationConstants {
  // Language codes
  static const String en = 'en';
  static const String ar = 'ar';

  // Authentication
  static const String phoneNumber = 'phoneNumber';
  static const String enterOTP = 'enterOTP';
  static const String verifyOTP = 'verifyOTP';

  // Scooter Related
  static const String findNearby = 'findNearby';
  static const String scanQR = 'scanQR';
  static const String startRide = 'startRide';
  static const String endRide = 'endRide';
  static const String pauseRide = 'pauseRide';
  static const String resumeRide = 'resumeRide';

  // Wallet
  static const String wallet = 'wallet';
  static const String addMoney = 'addMoney';
  static const String transactions = 'transactions';

  // Profile
  static const String profile = 'profile';
  static const String editProfile = 'editProfile';
  static const String language = 'language';

  // Ride Status
  static const String searching = 'searching';
  static const String rideInProgress = 'rideInProgress';
  static const String timeElapsed = 'timeElapsed';
  static const String distance = 'distance';
  static const String cost = 'cost';

  //Others
  static const String english = 'english';
  static const String arabic = 'arabic';

  //Settings
  static const String appearance = 'appearance';
  static const String darkMode = 'darkMode';
  static const String lightMode = 'lightMode';
  static const String langAndRegion = 'langAndRegion';
  static const String notifications = 'notifications';
  static const String pushNotifications = 'pushNotifications';
  static const String emailNotifications = 'emailNotifications';
  static const String about = 'about';
  static const String appVersion = 'appVersion';
  static const String privacyPolicy = 'privacyPolicy';
  static const String termsOfService = 'termsOfService';

  // OTP Verification
  static const String otpSentTo = 'otpSentTo';
  static const String resendIn = 'resendIn';
  static const String resendOtp = 'resendOtp';
  static const String verifyOtp = 'verifyOtp';
  static const String seconds = 'seconds';

  // Payment Methods
  static const String paymentMethods = 'paymentMethods';
  static const String seeAll = 'seeAll';
  static const String addNewPaymentMethod = 'addNewPaymentMethod';

  // New Constants
  static const String guest = 'guest';
  static const String account = 'account';
  static const String logout = 'logout';
  static const String gender = 'gender';

  // Drawer
  static const String helpAndSupport = 'helpAndSupport';
  static const String settings = 'settings';

  // Ride Related
  static const String rideEnded = 'rideEnded';
  static const String rideEndedSuccessfully = 'rideEndedSuccessfully';
  static const String failedToEndRide = 'failedToEndRide';
  static const String batteryLevel = 'batteryLevel';
  static const String lastStation = 'lastStation';
  static const String scooterStatus = 'scooterStatus';
  static const String ok = 'ok';
  static const String error = 'error';
  static const String endingRide = 'endingRide';
}
