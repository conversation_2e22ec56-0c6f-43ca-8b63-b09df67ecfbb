# Base API URL for authentication and core services
API_BASE_URL=https://api.example.com/api

# Scooter service API endpoint
API_SCOOTER_URL=https://scooter-api.example.com/api

# Payment processing API endpoint
API_PAYMENT_URL=https://payments-api.example.com/api

# WebSocket URL for real-time scooter communication
WSS_SCOOTER_URL=wss://scooter-api.example.com/api/

# Promotional offers and discounts API endpoint
API_PROMO_URL=https://promo-api.example.com/api

# Add any additional environment variables below
# MAPS_API_KEY=your_maps_api_key_here