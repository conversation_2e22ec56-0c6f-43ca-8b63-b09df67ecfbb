// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_stats_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userStatsNotifierHash() => r'c6619cc90d11c587a52c238d14fb546a37b73b46';

/// See also [UserStatsNotifier].
@ProviderFor(UserStatsNotifier)
final userStatsNotifierProvider =
    AsyncNotifierProvider<UserStatsNotifier, UserStats>.internal(
  UserStatsNotifier.new,
  name: r'userStatsNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userStatsNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UserStatsNotifier = AsyncNotifier<UserStats>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
