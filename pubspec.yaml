name: escooter
description: "A new Flutter project."
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ^3.6.0

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI and Navigation
  cupertino_icons: ^1.0.8
  google_fonts: ^6.1.0
  go_router: ^15.1.2

  # State Management
  flutter_riverpod: ^2.4.10
  riverpod_annotation: ^2.3.4

  # Data Persistence
  shared_preferences: ^2.2.2
  hive:
  hive_flutter:

  # Phone Number and Authentication
  intl: 0.20.2
  intl_phone_number_input: ^0.7.4
  pinput: ^5.0.1

  # Maps and Location
  # mapbox_gl: ^0.16.0
  geolocator: ^14.0.1
  flutter_map: ^7.0.2
  latlong2: ^0.9.0
  flutter_map_marker_cluster: ^1.3.0

  # Scanner
  mobile_scanner: ^7.0.0

  flutter_dotenv:
  http:

  # Dependency Injections
  freezed_annotation: ^2.4.1
  injectable: ^2.3.2
  get_it: ^8.0.3
  dartz:
  logger:
  web_socket_channel:
  
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0
  riverpod_generator: ^2.0.0
  build_runner: ^2.4.8
  injectable_generator:
  hive_generator: ^2.0.1
  flutter_launcher_icons:


flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icon/logo.png"
  min_sdk_android: 21

flutter:
  uses-material-design: true
  assets:
    - .env