// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'scooter_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$scootersNotifierHash() => r'6558aab6ac656fb0020bba8eed7d82b49027abec';

/// See also [ScootersNotifier].
@ProviderFor(ScootersNotifier)
final scootersNotifierProvider =
    AsyncNotifierProvider<ScootersNotifier, List<Scooter>>.internal(
  ScootersNotifier.new,
  name: r'scootersNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$scootersNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ScootersNotifier = AsyncNotifier<List<Scooter>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
