// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ride_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$ridesHash() => r'09bec8acb79cb0158d70b49dd794f1ce14eaa81d';

/// See also [Rides].
@ProviderFor(Rides)
final ridesProvider =
    AutoDisposeAsyncNotifierProvider<Rides, List<Ride>>.internal(
  Rides.new,
  name: r'ridesProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$ridesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Rides = AutoDisposeAsyncNotifier<List<Ride>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
